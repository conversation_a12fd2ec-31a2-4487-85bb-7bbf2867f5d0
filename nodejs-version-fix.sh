#!/bin/bash

# Node.js Version Fix for iOS Simulator Crash
# This script helps fix Node.js compatibility issues

echo "🔧 Node.js Version Fix Starting..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check current Node.js version
CURRENT_NODE=$(node --version)
print_step "Current Node.js version: $CURRENT_NODE"

# Check if Node.js version is problematic
if [[ "$CURRENT_NODE" == v24* ]] || [[ "$CURRENT_NODE" == v22* ]] || [[ "$CURRENT_NODE" == v21* ]]; then
    print_error "Node.js $CURRENT_NODE is incompatible with React Native 0.74.5 and Expo SDK 53"
    echo ""
    echo "🎯 SOLUTION OPTIONS:"
    echo ""
    
    # Option 1: Using nvm (recommended)
    echo "📋 OPTION 1: Use nvm (Node Version Manager) - RECOMMENDED"
    echo ""
    echo "1. Install nvm if not already installed:"
    echo "   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"
    echo ""
    echo "2. Restart terminal or run:"
    echo "   source ~/.bashrc"
    echo ""
    echo "3. Install and use Node.js 20 LTS:"
    echo "   nvm install 20"
    echo "   nvm use 20"
    echo "   nvm alias default 20"
    echo ""
    echo "4. Verify version:"
    echo "   node --version  # Should show v20.x.x"
    echo ""
    echo "5. Reinstall dependencies:"
    echo "   rm -rf node_modules package-lock.json"
    echo "   npm install"
    echo ""
    
    # Option 2: Using Homebrew
    echo "📋 OPTION 2: Use Homebrew"
    echo ""
    echo "1. Uninstall current Node.js:"
    echo "   brew uninstall node"
    echo ""
    echo "2. Install Node.js 20:"
    echo "   brew install node@20"
    echo "   brew link node@20 --force"
    echo ""
    echo "3. Verify version:"
    echo "   node --version  # Should show v20.x.x"
    echo ""
    echo "4. Reinstall dependencies:"
    echo "   rm -rf node_modules package-lock.json"
    echo "   npm install"
    echo ""
    
    # Option 3: Manual download
    echo "📋 OPTION 3: Manual Download"
    echo ""
    echo "1. Download Node.js 20 LTS from: https://nodejs.org/"
    echo "2. Install the downloaded package"
    echo "3. Verify version: node --version"
    echo "4. Reinstall dependencies"
    echo ""
    
    print_warning "After fixing Node.js version, run: ./complete-reset-procedure.sh"
    
elif [[ "$CURRENT_NODE" == v20* ]] || [[ "$CURRENT_NODE" == v18* ]]; then
    print_success "Node.js $CURRENT_NODE is compatible!"
    print_step "The issue might be elsewhere. Running alternative fixes..."
    
    # Alternative fix 1: Update Expo SDK to latest
    echo ""
    echo "🔧 ALTERNATIVE FIX 1: Update to latest Expo SDK"
    echo "npx expo install --fix"
    echo ""
    
    # Alternative fix 2: Use different React Native version
    echo "🔧 ALTERNATIVE FIX 2: Try React Native 0.73.x"
    echo "npm install react-native@0.73.6"
    echo ""
    
    # Alternative fix 3: Disable Hermes
    echo "🔧 ALTERNATIVE FIX 3: Disable Hermes engine"
    echo "Update app.json:"
    echo '  "ios": {'
    echo '    "jsEngine": "jsc"'
    echo '  }'
    echo ""
    
else
    print_success "Node.js $CURRENT_NODE should be compatible"
    print_step "Looking for other issues..."
fi

# Check if nvm is available
if command -v nvm &> /dev/null; then
    print_success "nvm is available"
    echo "Available Node.js versions:"
    nvm list
elif [ -f "$HOME/.nvm/nvm.sh" ]; then
    print_success "nvm is installed but not loaded"
    echo "Run: source ~/.nvm/nvm.sh"
else
    print_warning "nvm is not installed"
    echo "Install with: curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"
fi

echo ""
print_step "🎯 RECOMMENDED IMMEDIATE ACTION:"
echo ""
if [[ "$CURRENT_NODE" == v24* ]] || [[ "$CURRENT_NODE" == v22* ]] || [[ "$CURRENT_NODE" == v21* ]]; then
    echo "1. Install Node.js 20 LTS using nvm (see instructions above)"
    echo "2. Run: ./complete-reset-procedure.sh"
    echo "3. Test: npm start"
else
    echo "1. Run: ./complete-reset-procedure.sh"
    echo "2. Try alternative fixes above"
    echo "3. Check iOS Simulator Console logs"
fi
