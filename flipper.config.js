/**
 * Flipper configuration for React Native debugging
 * This file configures Flipper plugins and settings for better debugging experience
 */

module.exports = {
  // Flipper plugins configuration
  plugins: [
    // Network plugin for API debugging
    'flipper-plugin-network',
    
    // React DevTools for component inspection
    'flipper-plugin-react-devtools',
    
    // Layout inspector
    'flipper-plugin-layout',
    
    // Logs viewer
    'flipper-plugin-logs',
    
    // Crash reporter
    'flipper-plugin-crash-reporter',
    
    // Performance monitor
    'flipper-plugin-performance',
  ],
  
  // Server configuration
  server: {
    // Enable secure connection
    secure: true,
    
    // Port configuration
    port: 8089,
  },
  
  // Client configuration
  client: {
    // Enable client certificate verification
    certificateVerification: true,
    
    // Connection timeout
    connectionTimeout: 30000,
  },
  
  // Development settings
  development: {
    // Enable verbose logging
    verbose: true,
    
    // Enable hot reload for Flipper plugins
    hotReload: true,
  },
};
