const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  resolver: {
    // Enable support for web extensions for React Native Web compatibility
    platforms: ['ios', 'android', 'native', 'web'],
    alias: {
      // Add any path aliases here if needed
    },
  },
  transformer: {
    // Enable Hermes for better performance
    hermesCommand: 'hermes',
    // Enable inline requires for better performance
    inlineRequires: true,
  },
  server: {
    // Enable hot reloading
    enableVisualizer: false,
  },
  watchFolders: [
    // Add any additional folders to watch
  ],
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
