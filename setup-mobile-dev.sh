#!/bin/bash

# React Native Mobile Development Setup Script
# This script helps set up the development environment for running React Native on physical devices

echo "🚀 Setting up React Native for mobile device development..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    print_error "This script is designed for macOS. For other platforms, please follow the React Native documentation."
    exit 1
fi

print_status "Checking system requirements..."

# Check Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_success "Node.js is installed: $NODE_VERSION"
else
    print_error "Node.js is not installed. Please install Node.js 18 or later."
    exit 1
fi

# Check npm
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    print_success "npm is installed: $NPM_VERSION"
else
    print_error "npm is not installed."
    exit 1
fi

# Check Xcode
if command -v xcodebuild &> /dev/null; then
    XCODE_VERSION=$(xcodebuild -version | head -n 1)
    print_success "Xcode is installed: $XCODE_VERSION"
else
    print_warning "Xcode is not installed. You'll need it for iOS development."
    echo "Please install Xcode from the App Store."
fi

# Check for Xcode Command Line Tools
if xcode-select -p &> /dev/null; then
    print_success "Xcode Command Line Tools are installed"
else
    print_warning "Installing Xcode Command Line Tools..."
    xcode-select --install
fi

# Check CocoaPods
if command -v pod &> /dev/null; then
    POD_VERSION=$(pod --version)
    print_success "CocoaPods is installed: $POD_VERSION"
else
    print_warning "CocoaPods is not installed. Installing..."
    sudo gem install cocoapods
    if [ $? -eq 0 ]; then
        print_success "CocoaPods installed successfully"
    else
        print_error "Failed to install CocoaPods"
    fi
fi

# Check Watchman
if command -v watchman &> /dev/null; then
    WATCHMAN_VERSION=$(watchman --version)
    print_success "Watchman is installed: $WATCHMAN_VERSION"
else
    print_warning "Watchman is not installed. Installing via Homebrew..."
    if command -v brew &> /dev/null; then
        brew install watchman
        if [ $? -eq 0 ]; then
            print_success "Watchman installed successfully"
        else
            print_error "Failed to install Watchman"
        fi
    else
        print_error "Homebrew is not installed. Please install Homebrew first or install Watchman manually."
    fi
fi

# Check Java (for Android development)
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1)
    print_success "Java is installed: $JAVA_VERSION"
else
    print_warning "Java is not installed. You'll need it for Android development."
    echo "Please install Java 11 or later."
fi

# Install dependencies
print_status "Installing npm dependencies..."
npm install

if [ $? -eq 0 ]; then
    print_success "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Initialize React Native project for iOS and Android
print_status "Initializing native iOS and Android projects..."

# This will create the ios and android directories
npx react-native init TempProject --version 0.79.2 --skip-install

if [ -d "TempProject" ]; then
    # Copy the native directories
    cp -r TempProject/ios ./
    cp -r TempProject/android ./
    
    # Clean up
    rm -rf TempProject
    
    print_success "Native iOS and Android projects initialized"
else
    print_error "Failed to initialize native projects"
    exit 1
fi

# Update iOS project configuration
if [ -d "ios" ]; then
    print_status "Configuring iOS project..."
    cd ios
    pod install
    cd ..
    print_success "iOS project configured"
fi

print_success "Setup complete! 🎉"
echo ""
echo "Next steps:"
echo "1. For iOS development: Open ios/onRoadApp.xcworkspace in Xcode"
echo "2. For Android development: Install Android Studio and set up Android SDK"
echo "3. Connect your device and run:"
echo "   - iOS: npm run ios:device"
echo "   - Android: npm run android:device"
echo ""
echo "For detailed instructions, see the README-MOBILE-SETUP.md file."
