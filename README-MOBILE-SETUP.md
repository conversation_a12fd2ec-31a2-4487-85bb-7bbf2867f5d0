# React Native Mobile Development Setup

This guide will help you set up the onRoad React Native application for development and testing on physical mobile devices.

## 🎯 Overview

This project uses **React Native CLI** (bare workflow) which provides:
- Full native access and customization
- Better performance than managed workflows
- Direct device deployment
- Hot reloading and debugging support
- Flipper integration for advanced debugging

### ✅ What's Already Configured

The project has been pre-configured with:
- **Enhanced package.json** with device-specific scripts
- **Metro configuration** optimized for multi-platform development
- **Flipper integration** for advanced debugging
- **Development environment** configuration
- **Automated setup scripts** for easy initialization
- **Hot reloading** and debugging capabilities
- **Cross-platform support** (iOS, Android, Web)

### ⚠️ What You Need to Do

Since this is a bare React Native project, you need to:
1. **Initialize native projects** (iOS and Android directories)
2. **Install development tools** (Xcode, Android Studio, etc.)
3. **Set up your physical devices** for testing
4. **Run the setup scripts** provided

## 📋 Prerequisites

### For iOS Development (macOS only)
- **macOS** (required for iOS development)
- **Xcode** 14.0 or later (from App Store)
- **Xcode Command Line Tools**
- **CocoaPods** (dependency manager for iOS)
- **Node.js** 18 or later
- **Watchman** (file watching service)

### For Android Development (macOS, Windows, Linux)
- **Android Studio** with Android SDK
- **Java Development Kit (JDK)** 11 or later
- **Node.js** 18 or later
- **Watchman** (recommended)

## 🚀 Quick Setup

### Option 1: Automated Setup (macOS - Recommended)
Run the setup script to automatically install dependencies and configure the project:

```bash
# Make scripts executable
chmod +x setup-mobile-dev.sh init-native-projects.sh

# Run the complete setup
./setup-mobile-dev.sh
```

### Option 2: Initialize Native Projects Only
If you already have the development environment set up:

```bash
# Initialize iOS and Android native projects
./init-native-projects.sh

# Install dependencies
npm install
```

### Manual Setup

#### 1. Install System Dependencies

**Install Homebrew** (if not already installed):
```bash
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

**Install Watchman**:
```bash
brew install watchman
```

**Install CocoaPods** (for iOS):
```bash
sudo gem install cocoapods
```

#### 2. Install Project Dependencies
```bash
npm install
```

#### 3. Initialize Native Projects
Since this is a bare React Native project without native directories, you need to initialize them:

```bash
# This will create ios and android directories
npx react-native init onRoadApp --version 0.79.2 --skip-install

# Copy the native directories to your project
cp -r onRoadApp/ios ./
cp -r onRoadApp/android ./

# Clean up
rm -rf onRoadApp
```

#### 4. Configure iOS (macOS only)
```bash
cd ios
pod install
cd ..
```

## 📱 Running on Physical Devices

### iOS Device Setup

1. **Enable Developer Mode** on your iOS device:
   - Go to Settings > Privacy & Security > Developer Mode
   - Toggle on Developer Mode

2. **Connect your device** via USB to your Mac

3. **Trust your computer** when prompted on the device

4. **Run the app**:
   ```bash
   npm run ios:device
   ```

   Or specify a specific device:
   ```bash
   npx react-native run-ios --device "Your Device Name"
   ```

### Android Device Setup

1. **Enable Developer Options** on your Android device:
   - Go to Settings > About Phone
   - Tap "Build Number" 7 times
   - Go back to Settings > Developer Options

2. **Enable USB Debugging**:
   - In Developer Options, enable "USB Debugging"

3. **Connect your device** via USB

4. **Verify device connection**:
   ```bash
   adb devices
   ```

5. **Run the app**:
   ```bash
   npm run android:device
   ```

## 🔧 Development Commands

### Basic Commands
```bash
# Start Metro bundler
npm start

# Run on iOS device
npm run ios:device

# Run on Android device
npm run android:device

# Run on iOS simulator
npm run ios

# Run on Android emulator
npm run android
```

### Advanced Commands
```bash
# Build release version for iOS
npm run ios:release

# Build release version for Android
npm run android:release

# Clean build cache
npm run clean

# Reset Metro cache
npm run reset-cache
```

## 🐛 Debugging

### Flipper Integration
This project includes Flipper for advanced debugging:

1. **Install Flipper** from [flipper.fb.com](https://flipper.fb.com/)
2. **Start your app** on a device/simulator
3. **Open Flipper** - it should automatically detect your app
4. **Use debugging features**:
   - Network inspector
   - Layout inspector
   - Logs viewer
   - React DevTools

### React Native Debugger
For additional debugging capabilities:

1. **Install React Native Debugger**:
   ```bash
   brew install --cask react-native-debugger
   ```

2. **Enable debugging** in your app:
   - Shake device or press Cmd+D (iOS) / Cmd+M (Android)
   - Select "Debug"

### Hot Reloading
Hot reloading is enabled by default. To toggle:
- Shake device or press Cmd+D (iOS) / Cmd+M (Android)
- Select "Enable Hot Reloading"

## 🔥 Troubleshooting

### Common iOS Issues

**"Could not find iPhone" error**:
```bash
# List available devices
xcrun simctl list devices

# Run with specific device
npx react-native run-ios --device "Device Name"
```

**CocoaPods issues**:
```bash
cd ios
pod deintegrate
pod install
cd ..
```

### Common Android Issues

**Device not detected**:
```bash
# Check if device is connected
adb devices

# Restart adb server
adb kill-server
adb start-server
```

**Build failures**:
```bash
# Clean Android build
cd android
./gradlew clean
cd ..
```

### Metro Bundler Issues
```bash
# Reset Metro cache
npm run reset-cache

# Or manually
npx react-native start --reset-cache
```

## 📚 Additional Resources

- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [iOS Development Setup](https://reactnative.dev/docs/environment-setup?guide=native&platform=ios)
- [Android Development Setup](https://reactnative.dev/docs/environment-setup?guide=native&platform=android)
- [Flipper Documentation](https://flipper.fb.com/docs/getting-started/)

## 🆘 Getting Help

If you encounter issues:
1. Check the troubleshooting section above
2. Search [React Native GitHub Issues](https://github.com/facebook/react-native/issues)
3. Ask on [React Native Community Discord](https://discord.gg/react-native)
