import React from 'react';
import { SafeAreaView, StatusBar, StyleSheet } from 'react-native';
import LoginScreen from './src/screens/LoginScreen';

// Flipper integration for debugging (only in development)
if (__DEV__) {
  import('react-native-flipper').then(({ default: Flipper }) => {
    // Initialize Flipper connection
    Flipper.addPlugin({
      getId() { return 'onRoadApp'; },
      onConnect() {
        console.log('Flipper connected');
      },
      onDisconnect() {
        console.log('Flipper disconnected');
      },
      runInBackground() {
        return false;
      },
    });
  }).catch(() => {
    // Flipper not available, continue without it
    console.log('Flipper not available');
  });
}

// Mock navigation for now
const mockNavigation = {
  navigate: (screen) => {
    console.log(`Navigate to: ${screen}`);
  },
};

export default function App() {
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#ffffff" />
      <LoginScreen navigation={mockNavigation} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
});
