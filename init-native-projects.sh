#!/bin/bash

# Script to initialize native iOS and Android projects for React Native
# This script creates the necessary native directories and configurations

echo "🚀 Initializing native iOS and Android projects..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if native directories already exist
if [ -d "ios" ] && [ -d "android" ]; then
    print_warning "Native iOS and Android directories already exist."
    read -p "Do you want to recreate them? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Skipping native project initialization."
        exit 0
    fi
    
    print_status "Removing existing native directories..."
    rm -rf ios android
fi

# Create a temporary React Native project to extract native directories
print_status "Creating temporary React Native project..."

TEMP_PROJECT="TempOnRoadApp"
npx react-native init $TEMP_PROJECT --version 0.79.2 --skip-install

if [ ! -d "$TEMP_PROJECT" ]; then
    print_error "Failed to create temporary project"
    exit 1
fi

print_success "Temporary project created"

# Copy native directories
print_status "Copying native iOS and Android directories..."

if [ -d "$TEMP_PROJECT/ios" ]; then
    cp -r "$TEMP_PROJECT/ios" ./
    print_success "iOS directory copied"
else
    print_error "iOS directory not found in temporary project"
fi

if [ -d "$TEMP_PROJECT/android" ]; then
    cp -r "$TEMP_PROJECT/android" ./
    print_success "Android directory copied"
else
    print_error "Android directory not found in temporary project"
fi

# Clean up temporary project
print_status "Cleaning up temporary project..."
rm -rf $TEMP_PROJECT
print_success "Cleanup complete"

# Update project names and bundle identifiers
print_status "Updating project configuration..."

# Update iOS project name and bundle identifier
if [ -d "ios" ]; then
    # Find and replace project name in iOS files
    find ios -name "*.pbxproj" -exec sed -i '' 's/TempOnRoadApp/onRoadApp/g' {} \;
    find ios -name "*.plist" -exec sed -i '' 's/TempOnRoadApp/onRoadApp/g' {} \;
    find ios -name "*.h" -exec sed -i '' 's/TempOnRoadApp/onRoadApp/g' {} \;
    find ios -name "*.m" -exec sed -i '' 's/TempOnRoadApp/onRoadApp/g' {} \;
    
    # Rename directories if they exist
    if [ -d "ios/TempOnRoadApp" ]; then
        mv "ios/TempOnRoadApp" "ios/onRoadApp"
    fi
    
    if [ -d "ios/TempOnRoadApp.xcodeproj" ]; then
        mv "ios/TempOnRoadApp.xcodeproj" "ios/onRoadApp.xcodeproj"
    fi
    
    if [ -d "ios/TempOnRoadApp.xcworkspace" ]; then
        mv "ios/TempOnRoadApp.xcworkspace" "ios/onRoadApp.xcworkspace"
    fi
    
    print_success "iOS project configuration updated"
fi

# Update Android project name and package
if [ -d "android" ]; then
    # Update package name in Android files
    find android -name "*.java" -exec sed -i '' 's/com.temponroadapp/com.onroadapp/g' {} \;
    find android -name "*.xml" -exec sed -i '' 's/com.temponroadapp/com.onroadapp/g' {} \;
    find android -name "*.gradle" -exec sed -i '' 's/TempOnRoadApp/onRoadApp/g' {} \;
    find android -name "strings.xml" -exec sed -i '' 's/TempOnRoadApp/onRoad App/g' {} \;
    
    print_success "Android project configuration updated"
fi

# Install iOS dependencies
if [ -d "ios" ] && command -v pod &> /dev/null; then
    print_status "Installing iOS dependencies..."
    cd ios
    pod install
    cd ..
    print_success "iOS dependencies installed"
else
    print_warning "CocoaPods not found. Please install it and run 'cd ios && pod install'"
fi

print_success "Native project initialization complete! 🎉"
echo ""
echo "Next steps:"
echo "1. Install dependencies: npm install"
echo "2. Start Metro bundler: npm start"
echo "3. Run on device:"
echo "   - iOS: npm run ios:device"
echo "   - Android: npm run android:device"
