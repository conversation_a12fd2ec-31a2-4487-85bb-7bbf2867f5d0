#!/bin/bash

# Complete Environment Reset for iOS Simulator Crash Fix
# This script performs a comprehensive reset of the development environment

echo "🔄 Starting Complete Environment Reset..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Kill all running processes
print_step "1. Killing all Expo/Metro processes..."
pkill -f "expo" || true
pkill -f "metro" || true
pkill -f "react-native" || true
pkill -f "node.*start" || true
sleep 2
print_success "All processes killed"

# Step 2: Clear all Node.js caches
print_step "2. Clearing Node.js and npm caches..."
npm cache clean --force
yarn cache clean || true
print_success "Node.js caches cleared"

# Step 3: Remove node_modules and lock files
print_step "3. Removing node_modules and lock files..."
rm -rf node_modules
rm -f package-lock.json
rm -f yarn.lock
print_success "Dependencies removed"

# Step 4: Clear Expo caches
print_step "4. Clearing Expo caches..."
rm -rf ~/.expo
rm -rf .expo
rm -rf .expo-shared
print_success "Expo caches cleared"

# Step 5: Clear Metro caches
print_step "5. Clearing Metro bundler caches..."
rm -rf /tmp/metro-*
rm -rf /tmp/react-*
rm -rf /tmp/haste-*
print_success "Metro caches cleared"

# Step 6: Clear iOS Simulator data
print_step "6. Resetting iOS Simulator..."
if command -v xcrun &> /dev/null; then
    xcrun simctl shutdown all
    xcrun simctl erase all
    print_success "iOS Simulator reset"
else
    print_warning "Xcode not found, skipping iOS Simulator reset"
fi

# Step 7: Clear system temporary files
print_step "7. Clearing system temporary files..."
rm -rf /tmp/react-native-*
rm -rf /tmp/metro-cache-*
rm -rf /tmp/haste-map-*
print_success "System temp files cleared"

# Step 8: Reinstall dependencies
print_step "8. Reinstalling dependencies..."
npm install
if [ $? -eq 0 ]; then
    print_success "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Step 9: Verify installation
print_step "9. Verifying installation..."
if [ -d "node_modules" ] && [ -f "node_modules/.bin/expo" ]; then
    print_success "Installation verified"
else
    print_error "Installation verification failed"
    exit 1
fi

print_success "🎉 Complete environment reset finished!"
echo ""
echo "Next steps:"
echo "1. Run: npx expo start --clear"
echo "2. Test on physical device first (scan QR code)"
echo "3. Then test iOS simulator (press 'i')"
echo ""
echo "If issues persist, run the diagnostic script: ./ios-simulator-diagnostics.sh"
