module.exports = {
  // Configuration for React Native CLI
  project: {
    ios: {
      // Path to iOS project
      project: './ios/onRoadApp.xcworkspace',
    },
    android: {
      // Path to Android project
      sourceDir: './android',
      appName: 'app',
    },
  },
  
  // Assets configuration
  assets: ['./assets/fonts/', './assets/images/'],
  
  // Dependencies configuration
  dependencies: {
    // Disable auto-linking for specific packages if needed
    // 'react-native-vector-icons': {
    //   platforms: {
    //     android: {
    //       sourceDir: '../node_modules/react-native-vector-icons/android',
    //       packageImportPath: 'import io.github.react_native_vector_icons.VectorIconsPackage;',
    //     },
    //   },
    // },
  },
  
  // Commands configuration
  commands: [
    // Custom commands can be added here
  ],
};
